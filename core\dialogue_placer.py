#!/usr/bin/env python3
"""
Smart Dialogue Placement Engine for Manga Panels

This module provides intelligent dialogue bubble placement that:
- Detects important visual regions (faces, hands, characters)
- Places bubbles in empty areas while avoiding key features
- Dynamically sizes and positions bubbles based on text and panel structure
- Supports both color and black_and_white modes
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path
import json
from dataclasses import dataclass
import math


@dataclass
class DialogueBubble:
    """Represents a dialogue bubble with position and styling."""
    text: str
    x: int
    y: int
    width: int
    height: int
    speaker: str = "unknown"
    alignment: str = "left"  # left, right, center
    bubble_type: str = "speech"  # speech, thought, shout
    confidence: float = 1.0


@dataclass
class VisualRegion:
    """Represents an important visual region to avoid."""
    x: int
    y: int
    width: int
    height: int
    importance: float  # 0.0 to 1.0
    region_type: str  # face, hand, character, object


class DialoguePlacementEngine:
    """Main engine for intelligent dialogue bubble placement."""
    
    def __init__(self, color_mode: str = "color"):
        """Initialize the dialogue placement engine."""
        self.color_mode = color_mode
        self.face_cascade = None
        self.body_cascade = None
        
        # Load OpenCV cascades for detection
        self._load_detection_models()
        
        # Bubble styling based on color mode
        self.bubble_styles = self._get_bubble_styles()
        
        # Placement parameters
        self.min_bubble_size = (80, 40)
        self.max_bubble_size = (300, 150)
        self.margin = 10
        self.overlap_threshold = 0.3
        
    def _load_detection_models(self):
        """Load OpenCV detection models."""
        try:
            # Try to load face detection cascade
            face_cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
            
            # Try to load body detection cascade
            body_cascade_path = cv2.data.haarcascades + 'haarcascade_fullbody.xml'
            self.body_cascade = cv2.CascadeClassifier(body_cascade_path)
            
            print(f"✅ Loaded OpenCV detection models")
            
        except Exception as e:
            print(f"⚠️ Could not load detection models: {e}")
            self.face_cascade = None
            self.body_cascade = None
    
    def _get_bubble_styles(self) -> Dict[str, Any]:
        """Get bubble styling based on color mode."""
        if self.color_mode == "black_and_white":
            return {
                "bubble_color": (255, 255, 255),  # White bubble
                "border_color": (0, 0, 0),        # Black border
                "text_color": (0, 0, 0),          # Black text
                "border_thickness": 2,
                "font_scale": 0.6,
                "font_thickness": 1
            }
        else:  # color mode
            return {
                "bubble_color": (255, 255, 255),  # White bubble
                "border_color": (0, 0, 0),        # Black border
                "text_color": (0, 0, 0),          # Black text
                "border_thickness": 2,
                "font_scale": 0.6,
                "font_thickness": 1
            }
    
    def detect_visual_regions(self, image: np.ndarray) -> List[VisualRegion]:
        """Detect important visual regions to avoid when placing bubbles."""
        regions = []
        
        # Convert to grayscale for detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        
        # Detect faces
        if self.face_cascade is not None:
            faces = self.face_cascade.detectMultiScale(
                gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
            )
            
            for (x, y, w, h) in faces:
                regions.append(VisualRegion(
                    x=int(x), y=int(y), width=int(w), height=int(h),
                    importance=0.9, region_type="face"
                ))
        
        # Detect bodies/characters
        if self.body_cascade is not None:
            bodies = self.body_cascade.detectMultiScale(
                gray, scaleFactor=1.1, minNeighbors=3, minSize=(50, 100)
            )
            
            for (x, y, w, h) in bodies:
                regions.append(VisualRegion(
                    x=int(x), y=int(y), width=int(w), height=int(h),
                    importance=0.7, region_type="character"
                ))
        
        # Detect high-contrast regions (potential important objects)
        regions.extend(self._detect_high_contrast_regions(gray))
        
        print(f"   🔍 Detected {len(regions)} visual regions to avoid")
        return regions
    
    def _detect_high_contrast_regions(self, gray_image: np.ndarray) -> List[VisualRegion]:
        """Detect high-contrast regions that might be important."""
        regions = []
        
        try:
            # Apply edge detection
            edges = cv2.Canny(gray_image, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by size and add as regions
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # Only consider medium-sized regions
                if 1000 < area < 10000:
                    regions.append(VisualRegion(
                        x=x, y=y, width=w, height=h,
                        importance=0.5, region_type="object"
                    ))
            
        except Exception as e:
            print(f"   ⚠️ Error detecting high-contrast regions: {e}")
        
        return regions
    
    def calculate_text_size(self, text: str) -> Tuple[int, int]:
        """Calculate required bubble size for given text."""
        # Estimate text dimensions
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = self.bubble_styles["font_scale"]
        thickness = self.bubble_styles["font_thickness"]
        
        # Split text into lines (simple word wrapping)
        words = text.split()
        lines = []
        current_line = ""
        max_line_length = 25  # characters per line
        
        for word in words:
            if len(current_line + " " + word) <= max_line_length:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # Calculate dimensions
        max_width = 0
        total_height = 0
        line_height = 25
        
        for line in lines:
            (text_width, text_height), _ = cv2.getTextSize(line, font, font_scale, thickness)
            max_width = max(max_width, text_width)
            total_height += line_height
        
        # Add padding
        padding_x = 20
        padding_y = 15
        
        bubble_width = max_width + padding_x * 2
        bubble_height = total_height + padding_y * 2
        
        # Clamp to min/max sizes
        bubble_width = max(self.min_bubble_size[0], min(self.max_bubble_size[0], bubble_width))
        bubble_height = max(self.min_bubble_size[1], min(self.max_bubble_size[1], bubble_height))
        
        return bubble_width, bubble_height
    
    def find_optimal_position(self, image: np.ndarray, bubble_width: int, bubble_height: int,
                            visual_regions: List[VisualRegion], existing_bubbles: List[DialogueBubble]) -> Tuple[int, int, float]:
        """Find optimal position for a dialogue bubble."""
        height, width = image.shape[:2]
        best_position = (0, 0)
        best_score = -1
        
        # Grid search for optimal position
        step_size = 20
        
        for y in range(0, height - bubble_height, step_size):
            for x in range(0, width - bubble_width, step_size):
                score = self._evaluate_position(
                    x, y, bubble_width, bubble_height,
                    visual_regions, existing_bubbles, width, height
                )
                
                if score > best_score:
                    best_score = score
                    best_position = (x, y)
        
        return best_position[0], best_position[1], best_score
    
    def _evaluate_position(self, x: int, y: int, width: int, height: int,
                          visual_regions: List[VisualRegion], existing_bubbles: List[DialogueBubble],
                          image_width: int, image_height: int) -> float:
        """Evaluate how good a position is for placing a bubble."""
        score = 1.0
        
        # Check overlap with visual regions
        for region in visual_regions:
            overlap = self._calculate_overlap(
                x, y, width, height,
                region.x, region.y, region.width, region.height
            )
            if overlap > 0:
                penalty = overlap * region.importance
                score -= penalty
        
        # Check overlap with existing bubbles
        for bubble in existing_bubbles:
            overlap = self._calculate_overlap(
                x, y, width, height,
                bubble.x, bubble.y, bubble.width, bubble.height
            )
            if overlap > self.overlap_threshold:
                score -= overlap * 2  # Heavy penalty for bubble overlap
        
        # Prefer positions in upper areas (traditional manga layout)
        vertical_preference = 1.0 - (y / image_height) * 0.3
        score *= vertical_preference
        
        # Prefer positions away from edges
        edge_margin = 20
        if x < edge_margin or y < edge_margin or \
           x + width > image_width - edge_margin or y + height > image_height - edge_margin:
            score *= 0.7
        
        return max(0, score)
    
    def _calculate_overlap(self, x1: int, y1: int, w1: int, h1: int,
                          x2: int, y2: int, w2: int, h2: int) -> float:
        """Calculate overlap ratio between two rectangles."""
        # Calculate intersection
        left = max(x1, x2)
        top = max(y1, y2)
        right = min(x1 + w1, x2 + w2)
        bottom = min(y1 + h1, y2 + h2)
        
        if left >= right or top >= bottom:
            return 0.0
        
        intersection_area = (right - left) * (bottom - top)
        rect1_area = w1 * h1
        
        return intersection_area / rect1_area if rect1_area > 0 else 0.0

    def draw_bubble(self, image: np.ndarray, bubble: DialogueBubble) -> np.ndarray:
        """Draw a dialogue bubble on the image."""
        result_image = image.copy()

        # Draw bubble background (ellipse)
        center_x = bubble.x + bubble.width // 2
        center_y = bubble.y + bubble.height // 2
        axes = (bubble.width // 2 - 5, bubble.height // 2 - 5)

        # Draw filled ellipse for bubble
        cv2.ellipse(result_image, (center_x, center_y), axes, 0, 0, 360,
                   self.bubble_styles["bubble_color"], -1)

        # Draw border
        cv2.ellipse(result_image, (center_x, center_y), axes, 0, 0, 360,
                   self.bubble_styles["border_color"], self.bubble_styles["border_thickness"])

        # Draw text
        self._draw_text_in_bubble(result_image, bubble)

        return result_image

    def _draw_text_in_bubble(self, image: np.ndarray, bubble: DialogueBubble):
        """Draw text inside a dialogue bubble."""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = self.bubble_styles["font_scale"]
        thickness = self.bubble_styles["font_thickness"]
        color = self.bubble_styles["text_color"]

        # Split text into lines
        words = bubble.text.split()
        lines = []
        current_line = ""
        max_line_length = 25

        for word in words:
            if len(current_line + " " + word) <= max_line_length:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        # Calculate starting position
        line_height = 25
        total_text_height = len(lines) * line_height
        start_y = bubble.y + (bubble.height - total_text_height) // 2 + line_height

        # Draw each line
        for i, line in enumerate(lines):
            # Calculate text width for centering
            (text_width, _), _ = cv2.getTextSize(line, font, font_scale, thickness)
            text_x = bubble.x + (bubble.width - text_width) // 2
            text_y = start_y + i * line_height

            cv2.putText(image, line, (text_x, text_y), font, font_scale, color, thickness)

    def place_dialogue(self, image_path: str, dialogue_lines: List[str],
                      speakers: List[str] = None) -> Tuple[np.ndarray, List[DialogueBubble], Dict[str, Any]]:
        """
        Main method to place dialogue bubbles on an image.

        Args:
            image_path: Path to the input image
            dialogue_lines: List of dialogue text
            speakers: List of speaker names (optional)

        Returns:
            Tuple of (processed_image, bubble_list, placement_metadata)
        """
        print(f"   💬 Placing {len(dialogue_lines)} dialogue bubbles")

        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")

        # Detect visual regions to avoid
        visual_regions = self.detect_visual_regions(image)

        # Initialize placement tracking
        placed_bubbles = []
        placement_scores = []

        # Process each dialogue line
        for i, dialogue in enumerate(dialogue_lines):
            if not dialogue.strip():
                continue

            speaker = speakers[i] if speakers and i < len(speakers) else f"speaker_{i+1}"

            # Calculate bubble size
            bubble_width, bubble_height = self.calculate_text_size(dialogue)

            # Find optimal position
            x, y, score = self.find_optimal_position(
                image, bubble_width, bubble_height, visual_regions, placed_bubbles
            )

            # Create bubble
            bubble = DialogueBubble(
                text=dialogue,
                x=x, y=y,
                width=bubble_width, height=bubble_height,
                speaker=speaker,
                confidence=score
            )

            # Draw bubble on image
            image = self.draw_bubble(image, bubble)

            placed_bubbles.append(bubble)
            placement_scores.append(score)

            print(f"     Bubble {i+1}: '{dialogue[:30]}...' at ({x}, {y}) score: {score:.3f}")

        # Generate placement metadata
        metadata = self._generate_placement_metadata(placed_bubbles, placement_scores, visual_regions)

        return image, placed_bubbles, metadata

    def _generate_placement_metadata(self, bubbles: List[DialogueBubble],
                                   scores: List[float], regions: List[VisualRegion]) -> Dict[str, Any]:
        """Generate metadata about the dialogue placement."""
        if not bubbles:
            return {
                "bubble_count": 0,
                "average_placement_score": 0.0,
                "bubble_overlap_score": 1.0,
                "visual_regions_detected": len(regions),
                "placement_quality": "no_dialogue"
            }

        avg_score = sum(scores) / len(scores)

        # Calculate bubble overlap score
        overlap_score = self._calculate_bubble_overlap_score(bubbles)

        # Determine placement quality
        if avg_score >= 0.8:
            quality = "excellent"
        elif avg_score >= 0.6:
            quality = "good"
        elif avg_score >= 0.4:
            quality = "fair"
        else:
            quality = "poor"

        return {
            "bubble_count": len(bubbles),
            "average_placement_score": round(avg_score, 3),
            "bubble_overlap_score": round(overlap_score, 3),
            "visual_regions_detected": len(regions),
            "placement_quality": quality,
            "color_mode": self.color_mode,
            "individual_scores": [round(s, 3) for s in scores],
            "bubble_positions": [
                {"x": b.x, "y": b.y, "width": b.width, "height": b.height, "speaker": b.speaker}
                for b in bubbles
            ]
        }

    def _calculate_bubble_overlap_score(self, bubbles: List[DialogueBubble]) -> float:
        """Calculate how well bubbles avoid overlapping each other."""
        if len(bubbles) <= 1:
            return 1.0

        total_overlap = 0.0
        comparisons = 0

        for i in range(len(bubbles)):
            for j in range(i + 1, len(bubbles)):
                overlap = self._calculate_overlap(
                    bubbles[i].x, bubbles[i].y, bubbles[i].width, bubbles[i].height,
                    bubbles[j].x, bubbles[j].y, bubbles[j].width, bubbles[j].height
                )
                total_overlap += overlap
                comparisons += 1

        avg_overlap = total_overlap / comparisons if comparisons > 0 else 0.0
        return max(0.0, 1.0 - avg_overlap)


def create_sample_dialogue() -> List[str]:
    """Create sample dialogue for testing."""
    return [
        "What is this ancient symbol?",
        "It looks like a warning...",
        "We should be careful here."
    ]


def test_dialogue_placement():
    """Test the dialogue placement system."""
    print("🧪 Testing Dialogue Placement System")

    # Create test engine
    engine = DialoguePlacementEngine("black_and_white")

    # Test text size calculation
    test_text = "This is a test dialogue bubble"
    width, height = engine.calculate_text_size(test_text)
    print(f"   Text size for '{test_text}': {width}x{height}")

    # Test bubble creation
    bubble = DialogueBubble(
        text=test_text, x=50, y=50, width=width, height=height,
        speaker="test_speaker"
    )
    print(f"   Created bubble: {bubble}")

    print("✅ Dialogue placement system test completed")


if __name__ == "__main__":
    test_dialogue_placement()
